import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:monitor',
      order: 3,
      title: '大屏管理',
    },
    name: 'Screen',
    path: '/screen',
    children: [
      {
        name: 'ScreenList',
        path: '/list',
        component: () => import('#/views/screen/list/index.vue'),
        meta: {
          icon: 'lucide:list',
          title: '大屏列表',
        },
      },
      {
        name: 'ScreenDesigner',
        path: '/designer',
        component: () => import('#/views/screen/designer/index.vue'),
        meta: {
          icon: 'lucide:layout',
          title: '大屏设计器',
        },
      },
      {
        name: 'ScreenPreview',
        path: '/preview/:id?',
        component: () => import('#/views/screen/preview/index.vue'),
        meta: {
          icon: 'lucide:eye',
          title: '大屏预览',
          hideInMenu: true,
        },
      },
      {
        name: 'Template<PERSON><PERSON><PERSON>',
        path: '/template',
        component: () => import('#/views/screen/template/index.vue'),
        meta: {
          icon: 'lucide:layout-template',
          title: '模板管理',
        },
      },
    ],
  },
];

export default routes;
