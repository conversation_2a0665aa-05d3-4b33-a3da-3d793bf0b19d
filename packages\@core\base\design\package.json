{"name": "@vben-core/design", "version": "5.5.8", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/@vben-core/base/design"}, "license": "MIT", "type": "module", "scripts": {"build": "pnpm vite build", "prepublishOnly": "npm run build"}, "files": ["dist", "src"], "main": "./dist/index.mjs", "module": "./dist/index.mjs", "exports": {"./bem": {"development": "./src/scss-bem/bem.scss", "default": "./dist/bem.scss"}, ".": {"types": "./src/index.ts", "development": "./src/index.ts", "default": "./dist/design.css"}}, "publishConfig": {"exports": {".": {"default": "./dist/index.mjs"}}}}