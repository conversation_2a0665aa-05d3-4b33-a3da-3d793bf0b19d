{"name": "@vben-core/icons", "version": "5.5.8", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/@vben-core/base/icons"}, "license": "MIT", "type": "module", "scripts": {"build": "pnpm unbuild"}, "files": ["dist"], "main": "./dist/index.mjs", "module": "./dist/index.mjs", "exports": {".": {"types": "./src/index.ts", "development": "./src/index.ts", "default": "./dist/index.mjs"}}, "publishConfig": {"exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}}}, "dependencies": {"@iconify/vue": "catalog:", "lucide-vue-next": "catalog:", "vue": "catalog:"}}