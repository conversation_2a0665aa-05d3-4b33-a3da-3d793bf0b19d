{"name": "@vben/turbo-run", "version": "5.5.8", "private": true, "license": "MIT", "type": "module", "scripts": {"stub": "pnpm unbuild --stub"}, "files": ["dist"], "bin": {"turbo-run": "./bin/turbo-run.mjs"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "exports": {".": {"default": "./dist/index.mjs"}, "./package.json": "./package.json"}, "dependencies": {"@clack/prompts": "catalog:", "@vben/node-utils": "workspace:*", "cac": "catalog:"}}