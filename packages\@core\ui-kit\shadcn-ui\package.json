{"name": "@vben-core/shadcn-ui", "version": "5.5.8", "#main": "./dist/index.mjs", "#module": "./dist/index.mjs", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/@vben-core/uikit/shadcn-ui"}, "license": "MIT", "type": "module", "scripts": {"#build": "pnpm unbuild", "#prepublishOnly": "npm run build"}, "files": ["dist"], "sideEffects": ["**/*.css"], "main": "./src/index.ts", "module": "./src/index.ts", "exports": {".": {"types": "./src/index.ts", "development": "./src/index.ts", "default": "./src/index.ts", "//default": "./dist/index.mjs"}}, "publishConfig": {"exports": {".": {"default": "./src/index.ts"}}}, "dependencies": {"@vben-core/composables": "workspace:*", "@vben-core/icons": "workspace:*", "@vben-core/shared": "workspace:*", "@vben-core/typings": "workspace:*", "@vueuse/core": "catalog:", "class-variance-authority": "catalog:", "lucide-vue-next": "catalog:", "radix-vue": "catalog:", "vee-validate": "catalog:", "vue": "catalog:"}}